const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const telegramService = require('../../../../services/telegram');
const cacheService = require('../../../../services/cache');
const storageService = require('../../../../services/storageService');
const metadataUtil = require('../../../../util/metadata');
const fs = require('fs');
const path = require('path');

module.exports = async (req, res) => {
  // Record upload start time for metadata
  const uploadStartTime = new Date();

  try {
    if (!req.file) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'No file uploaded'
      });
    }

      const { originalname, filename, path: filePath, size, mimetype } = req.file;
      const parentId = req.body.parentId || null;
      const userId = req.user ? req.user.id : null;

      // Use originalFileName from form data if provided (to avoid encoding issues)
      // Otherwise fall back to multer's originalname
      const actualOriginalName = req.body.originalFileName || originalname;

      console.log('File upload details:');
      console.log('- Multer originalname:', originalname);
      console.log('- Form originalFileName:', req.body.originalFileName);
      console.log('- Using filename:', actualOriginalName);

      // Check storage quota before upload
      const quotaCheck = await storageService.checkStorageQuota(userId, size);
      if (!quotaCheck.hasEnoughSpace) {
        // Clean up uploaded file
        fs.unlinkSync(filePath);
        return res.status(413).json({
          code: CONSTANTS.CODE.INVALID_PARAMS,
          message: `Storage quota exceeded. You need ${storageService.formatBytes(quotaCheck.wouldExceedBy)} more space. Available: ${storageService.formatBytes(quotaCheck.availableSpace)}`,
          data: {
            storageUsed: quotaCheck.storageUsed,
            storageQuota: quotaCheck.storageQuota,
            availableSpace: quotaCheck.availableSpace,
            fileSize: quotaCheck.fileSize,
            wouldExceedBy: quotaCheck.wouldExceedBy
          }
        });
      }

      // Validate parent folder if provided
      if (parentId) {
        const FolderModel = require('../../../../models/folder');
        const parentFolder = await FolderModel.findOne({
          _id: parentId,
          isDeleted: false,
          ownerId: userId // Ensure user can only upload to their own folders
        });

        if (!parentFolder) {
          // Clean up uploaded file
          fs.unlinkSync(filePath);
          return res.status(400).json({
            code: CONSTANTS.CODE.INVALID_PARAMS,
            message: 'Parent folder not found or access denied'
          });
        }
      }

      // Upload to Telegram and get full response
      const telegramResponse = await telegramService.uploadFileWithMetadata(filePath, actualOriginalName);

      // Extract file metadata from Telegram response
      const fileMetadata = {};
      if (telegramResponse.document) {
        fileMetadata.fileUniqueId = telegramResponse.document.file_unique_id;
        fileMetadata.fileName = telegramResponse.document.file_name;
        if (telegramResponse.document.thumbnail) {
          fileMetadata.thumbnail = telegramResponse.document.thumbnail.file_id;
          fileMetadata.width = telegramResponse.document.thumbnail.width;
          fileMetadata.height = telegramResponse.document.thumbnail.height;
        }
      } else if (telegramResponse.video) {
        fileMetadata.fileUniqueId = telegramResponse.video.file_unique_id;
        fileMetadata.fileName = telegramResponse.video.file_name;
        fileMetadata.width = telegramResponse.video.width;
        fileMetadata.height = telegramResponse.video.height;
        fileMetadata.duration = telegramResponse.video.duration;
        if (telegramResponse.video.thumbnail) {
          fileMetadata.thumbnail = telegramResponse.video.thumbnail.file_id;
        }
      } else if (telegramResponse.photo) {
        const largestPhoto = telegramResponse.photo[telegramResponse.photo.length - 1];
        fileMetadata.fileUniqueId = largestPhoto.file_unique_id;
        fileMetadata.width = largestPhoto.width;
        fileMetadata.height = largestPhoto.height;
      } else if (telegramResponse.audio) {
        fileMetadata.fileUniqueId = telegramResponse.audio.file_unique_id;
        fileMetadata.fileName = telegramResponse.audio.file_name;
        fileMetadata.duration = telegramResponse.audio.duration;
        fileMetadata.performer = telegramResponse.audio.performer;
        fileMetadata.title = telegramResponse.audio.title;
        if (telegramResponse.audio.thumbnail) {
          fileMetadata.thumbnail = telegramResponse.audio.thumbnail.file_id;
        }
      } else if (telegramResponse.sticker) {
        fileMetadata.fileUniqueId = telegramResponse.sticker.file_unique_id;
        fileMetadata.width = telegramResponse.sticker.width;
        fileMetadata.height = telegramResponse.sticker.height;
      }

      // Collect comprehensive upload metadata
      let uploadMetadata = {};
      let fileHash = {};
      let exifData = {};
      let imageMetadata = {};

      try {
        const metadataResult = await metadataUtil.collectUploadMetadata(req, req.file, uploadStartTime);
        uploadMetadata = metadataResult.uploadMetadata;
        fileHash = metadataResult.fileHash;
        exifData = metadataResult.exifData || {};
        imageMetadata = metadataResult.imageMetadata || {};

        // Log metadata for debugging
        global.logger.logInfo(`Upload metadata collected: ${metadataUtil.formatMetadataForLog(metadataResult)}`);

        // Log EXIF data if available
        if (exifData && exifData.gps && exifData.gps.latitude) {
          global.logger.logInfo(`EXIF GPS found: ${exifData.gps.latitude}, ${exifData.gps.longitude}`);
        }
        if (exifData && exifData.dateTime && exifData.dateTime.dateTimeOriginal) {
          global.logger.logInfo(`EXIF date: ${exifData.dateTime.dateTimeOriginal}`);
        }
        if (exifData && exifData.camera && exifData.camera.make) {
          global.logger.logInfo(`EXIF camera: ${exifData.camera.make} ${exifData.camera.model}`);
        }
      } catch (metadataError) {
        global.logger.logError(`Error collecting upload metadata: ${metadataError.message}`);
        // Continue with upload even if metadata collection fails
      }

      // Create file record in database
      const FileModel = require('../../../../models/file');
      const fileRecord = new FileModel({
        telegramFileId: telegramResponse.fileId,
        originalFileName: actualOriginalName,
        fileSize: size,
        mimeType: mimetype,
        parentId: parentId || null,
        ownerId: userId,
        uploadDate: new Date(),
        telegramMetadata: telegramResponse.fullResponse, // Store complete Telegram response
        fileMetadata: fileMetadata,
        fileHash: fileHash, // Add file hashes
        uploadMetadata: uploadMetadata, // Add comprehensive upload metadata
        exifData: exifData, // Add EXIF data from images/videos
        imageMetadata: imageMetadata // Add additional image metadata
      });

      await fileRecord.save();

      // Update user storage usage
      await storageService.addStorageUsage(userId, size);

      // Clean up local file
      fs.unlinkSync(filePath);

      // Clear cache for parent folder
      await cacheService.clearFolderCache(parentId);
      await cacheService.clearSearchCache();

      global.logger.logInfo(`File uploaded successfully: ${actualOriginalName}, ID: ${fileRecord._id}, Size: ${size} bytes`);

    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        id: fileRecord._id,
        telegramFileId: fileRecord.telegramFileId,
        originalFileName: fileRecord.originalFileName,
        fileSize: fileRecord.fileSize,
        mimeType: fileRecord.mimeType,
        uploadDate: fileRecord.uploadDate,
        parentId: fileRecord.parentId
      },
      message: 'File uploaded successfully'
    });

  } catch (error) {
    logger.logInfo(['files/upload error', error.message], __dirname);
    console.error('Upload error:', error);

    // Clean up local file if exists
    if (req.file && req.file.path && fs.existsSync(req.file.path)) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (cleanupError) {
        logger.logInfo(['files/upload cleanup error', cleanupError.message], __dirname);
        console.error('Cleanup error:', cleanupError);
      }
    }

    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: error.message || MESSAGES.SYSTEM.ERROR
    });
  }
};
